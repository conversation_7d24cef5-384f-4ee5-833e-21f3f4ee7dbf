"""
param 模块 - 策略配置参数管理

这是一个模拟版本的 param 模块，提供与原始 quantapi.param 相同的接口和方法。
主要用于测试环境中替代原始的 param 模块，支持策略参数的获取和管理。

功能特点：
1. 提供 get() 方法获取策略配置参数
2. 支持参数分组管理（paramKey）
3. 支持矩阵参数获取（Excel数据）
4. 提供默认值机制
5. 支持中英文参数名称

作者: AI Assistant
创建时间: 2025-08-12
"""

import os
import json
import numpy as np
import pandas as pd
from typing import Any, Optional, Dict, List, Union
import threading
from pathlib import Path

# 线程锁，确保参数访问的线程安全
_param_lock = threading.Lock()

class ParamManager:
    """参数管理器"""
    
    def __init__(self):
        self._params = {}  # 存储所有参数
        self._default_group = "默认参数"
        self._load_default_params()

    def _load_default_params(self):
        """加载默认参数配置"""
        # 基于原始代码中的参数设置默认值
        default_params = {
            "默认参数": {
                # 策略基础参数
                "策略合约": "XAUUSD",
                "合约渠道": "MT4", 
                "bar数据频率": "1m",
            }
        }
        
        self._params.update(default_params)
        self._param_groups = list(default_params.keys())

    def get_param(self, key: str, param_key: str = "默认参数") -> Any:
        """
        获取参数值
        
        Args:
            key: 参数名称
            param_key: 参数分组名称
            
        Returns:
            参数值，如果不存在返回None
        """
        with _param_lock:
            if param_key in self._params:
                return self._params[param_key].get(key)
            return None
    
    def set_param(self, key: str, value: Any, param_key: str = "默认参数"):
        """
        设置参数值
        
        Args:
            key: 参数名称
            value: 参数值
            param_key: 参数分组名称
        """
        with _param_lock:
            if param_key not in self._params:
                self._params[param_key] = {}
                if param_key not in self._param_groups:
                    self._param_groups.append(param_key)
            
            self._params[param_key][key] = value

# 全局参数管理器实例
_param_manager = ParamManager()

def get(key: str, param_key: str = "默认参数") -> Any:
    """
    策略配置的参数获取函数，在策略维度使用客户端配置的启动参数
    
    Args:
        key (str): 策略配置的参数名称
        param_key (str): 指定参数分组的key，即tab页名称，默认为"默认参数"
        
    Returns:
        策略配置的参数值，如果参数不存在返回None

    """
    return _param_manager.get_param(key, param_key)


if __name__ == '__main__':
    print(get("策略合约"))